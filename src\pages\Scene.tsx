import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Typo<PERSON>,
  Button,
  App,
  Spin,
  Empty,
  Tag,
  Layout,
  Avatar,
  Input,
  List,
  Tooltip,
  Modal,
  Switch,
  Dropdown,
} from "antd";
import type { MenuProps } from 'antd';
import { useParams, useNavigate } from "react-router-dom";
import {
  getSceneData,
  createSpeech,
  createSpeechJobs,
  getSpeechJobs,
  processJob,
  deleteSpeechBatch,
  createSceneComments,
  deleteSceneComments,
  type SceneBasicResponse,
  type CharacterInfo,
  type CreateSpeechRequest,
  type JobInfo,
  type SceneCommentsRequest,
} from "../services/scene";
import {
  updateExerciseLogStatus,
  getTeacherInfo,
  getTeacherAvatar,
} from "../services/exercise";
import { ttsService, TTSStatus } from "../services/ttsService";
import GuideDrawer from "../components/GuideDrawer";
import AutoHideScrollbar from "../components/AutoHideScrollbar";
import ScrollButtons from "../components/ScrollButtons/ScrollButtons";
import SpeechRecognitionButton from "../components/SpeechRecognitionButton";
import MarkdownRenderer from "../components/MarkdownRenderer";
import StreamingMarkdownRenderer from "../components/StreamingMarkdownRenderer";
import {
  RollbackOutlined,
  ClockCircleOutlined,
  UserOutlined,
  CompassOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SoundOutlined,
  SoundFilled,
  RobotOutlined,
  RedoOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  SendOutlined,
  CopyOutlined,
  DeleteOutlined,
  CloseOutlined,
  CommentOutlined,
  EditOutlined,
} from "@ant-design/icons";
import { MdAlternateEmail } from "react-icons/md";
import { MainLayout } from "../layouts";
import "../styles/Scene.css";

const { Title, Text } = Typography;
const { Sider, Content } = Layout;
const { TextArea } = Input;

const Scene: React.FC = () => {
  const { exerciseId, classId } = useParams<{
    exerciseId: string;
    classId: string;
  }>();
  const navigate = useNavigate();
  const { message, modal } = App.useApp();
  const [sceneData, setSceneData] = useState<SceneBasicResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [selectedCharacter, setSelectedCharacter] =
    useState<CharacterInfo | null>(null);
  const [messageText, setMessageText] = useState("");
  const [sending, setSending] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [autoVoice, setAutoVoice] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [fullscreenTooltipOpen, setFullscreenTooltipOpen] = useState(false);
  const [mentionTooltipOpen, setMentionTooltipOpen] = useState(false);
  const [guideDrawerVisible, setGuideDrawerVisible] = useState(false);
  const [profileModalVisible, setProfileModalVisible] = useState(false);
  const [selectedCharacterForProfile, setSelectedCharacterForProfile] =
    useState<CharacterInfo | null>(null);
  const [hasAIComment, setHasAIComment] = useState(false);
  const [showCommentsPanel, setShowCommentsPanel] = useState(false); // 是否显示点评面板
  const [streamingComments, setStreamingComments] = useState(''); // 流式点评内容
  const [isCommenting, setIsCommenting] = useState(false); // 是否正在生成点评
  const [inputContainerHeight, setInputContainerHeight] = useState(80); // 输入框容器高度，默认为最小高度（一行+function bar）
  const [userBaseHeight, setUserBaseHeight] = useState(80); // 用户设置的基础高度（不包含标签区域）
  const [isDragging, setIsDragging] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [selectedMentionCharacters, setSelectedMentionCharacters] = useState<CharacterInfo[]>([]); // 指定发言的角色列表
  const [streamingContents, setStreamingContents] = useState<Record<number, string>>({}); // 流式内容，key为角色ID
  const [processingJobs, setProcessingJobs] = useState<Set<number>>(new Set()); // 正在处理的任务ID集合
  const [lastSpeechId, setLastSpeechId] = useState<number | null>(null); // 最后一次发言的ID，用于检查未完成任务
  const [failedJobs, setFailedJobs] = useState<Set<number>>(new Set()); // 失败的任务ID集合
  const [jobRetryCount, setJobRetryCount] = useState<Map<number, number>>(new Map()); // 任务重试次数记录
  const [waitingForParticipants, setWaitingForParticipants] = useState(false); // 等待参会者发言状态
  const [processingCharacters, setProcessingCharacters] = useState<Set<number>>(new Set()); // 正在处理Job的角色ID集合
  const [playingSpeechId, setPlayingSpeechId] = useState<number | null>(null); // 正在播放语音的发言ID
  const [ttsStatus, setTtsStatus] = useState<'idle' | 'connecting' | 'connected' | 'playing' | 'error' | 'completed'>('idle'); // TTS状态
  const messagesEndRef = useRef<HTMLDivElement>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const scrollContainerRef = useRef<any>(null);
  const chatContentRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null); // 添加textarea的ref
  const lastProcessedSpeechIdRef = useRef<number | null>(null); // 防止重复处理同一个speechId
  const commentsContentRef = useRef<HTMLDivElement>(null); // 点评内容区域的ref

  // 拖拽相关的ref
  const handleDragMoveRef = useRef<(e: MouseEvent) => void>(() => {});
  const handleDragEndRef = useRef<() => void>(() => {});
  const initialMouseYRef = useRef<number>(0);
  const initialHeightRef = useRef<number>(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // 包装函数，确保调用最新的引用
  const handleDragMove = useCallback((e: MouseEvent) => {
    handleDragMoveRef.current?.(e);
  }, []);

  const handleDragEnd = useCallback(() => {
    handleDragEndRef.current?.();
  }, []);

  // 开始拖拽
  const handleDragStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);

    // 记录初始鼠标位置和容器高度
    initialMouseYRef.current = e.clientY;
    initialHeightRef.current = inputContainerHeight;

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
  }, [inputContainerHeight, handleDragMove, handleDragEnd]);

  // 拖拽过程
  handleDragMoveRef.current = (e: MouseEvent) => {
    if (!isDragging) return;

    // 计算鼠标垂直移动的距离（向上为负，向下为正）
    const deltaY = e.clientY - initialMouseYRef.current;

    // 根据鼠标移动距离调整高度（向上拖动增加高度，向下拖动减少高度）
    const newHeight = Math.max(80, Math.min(260, initialHeightRef.current - deltaY));

    // 更新容器高度
    setInputContainerHeight(newHeight);
    
    // 计算并更新用户基础高度（减去标签区域高度，如果有的话）
    const tagAreaHeight = 44;
    const newUserBaseHeight = selectedMentionCharacters.length > 0 
      ? Math.max(80, newHeight - tagAreaHeight)
      : newHeight;
    setUserBaseHeight(newUserBaseHeight);
  };

  // 结束拖拽
  handleDragEndRef.current = () => {
    setIsDragging(false);
    document.removeEventListener('mousemove', handleDragMoveRef.current!);
    document.removeEventListener('mouseup', handleDragEndRef.current!);
  };

  // 组件卸载时清理事件监听和TTS服务
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleDragMove);
      document.removeEventListener('mouseup', handleDragEnd);
      // 清理TTS服务
      ttsService.destroy();
    };
  }, [handleDragMove, handleDragEnd]);

  // 监听角色标签变化，基于用户基础高度调整容器高度
  useEffect(() => {
    const tagAreaHeight = 44; // 标签区域高度
    
    // 记录当前滚动位置（在高度变化前）
    const currentScrollTop = textareaRef.current?.scrollTop || 0;
    const currentScrollHeight = textareaRef.current?.scrollHeight || 0;
    const currentClientHeight = textareaRef.current?.clientHeight || 0;
    const wasAtBottom = currentScrollTop + currentClientHeight >= currentScrollHeight - 5; // 5px容差
    
    // 基于用户基础高度和标签状态计算实际容器高度
    const newContainerHeight = selectedMentionCharacters.length > 0 
      ? userBaseHeight + tagAreaHeight  // 有标签时：基础高度 + 标签区域高度
      : userBaseHeight;                 // 无标签时：只用基础高度
    
    setInputContainerHeight(newContainerHeight);
    
    // 延迟调整滚动位置，确保DOM完全更新
    const timer = setTimeout(() => {
      if (textareaRef.current && wasAtBottom) {
        // 如果之前在底部，滚动到新的底部
        textareaRef.current.scrollTop = textareaRef.current.scrollHeight;
      }
    }, 100); // 增加延迟时间确保DOM更新完成

    return () => clearTimeout(timer);
  }, [selectedMentionCharacters.length, userBaseHeight]);

  // 滚动到消息底部
  const scrollToBottom = useCallback(() => {
    // 使用setTimeout确保DOM更新完成后再滚动
    setTimeout(() => {
      if (scrollContainerRef.current?.scrollerElement) {
        // 使用AutoHideScrollbar的滚动元素
        const scroller = scrollContainerRef.current.scrollerElement;
        scroller.scrollTop = scroller.scrollHeight;
      } else if (messagesEndRef.current) {
        // 备用方案：使用原有的scrollIntoView
        messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
      }
    }, 50); // 50ms延迟确保DOM更新完成
  }, []);

  // 滚动点评内容到底部
  const scrollCommentsToBottom = useCallback(() => {
    setTimeout(() => {
      if (commentsContentRef.current) {
        commentsContentRef.current.scrollTop = commentsContentRef.current.scrollHeight;
      }
    }, 50);
  }, []);

  // 当场景数据的发言列表变化时滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [sceneData?.speeches, scrollToBottom]);

  // 当流式内容变化时滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [streamingContents, scrollToBottom]);

  // 当正在处理的角色变化时滚动到底部（新增机器人气泡框时）
  useEffect(() => {
    scrollToBottom();
  }, [processingCharacters, scrollToBottom]);



  // 获取场景数据
  const fetchSceneData = useCallback(async () => {
    if (!exerciseId || !classId) return;

    try {
      setLoading(true);
      const data = await getSceneData(Number(exerciseId), Number(classId));
      setSceneData(data);

      // 根据comments字段设置hasAIComment状态
      setHasAIComment(!!data.comments);
      // 如果有comments，默认显示点评面板
      setShowCommentsPanel(!!data.comments);

      // 如果有角色且没有选中角色，默认选中第一个角色
      // 使用函数式更新来避免依赖selectedCharacter
      setSelectedCharacter(prev => {
        if (!prev && data.characters.length > 0) {
          return data.characters[0];
        }
        return prev;
      });
    } catch (error) {
      console.error("获取场景数据失败:", error);
      message.error("获取场景数据失败");
    } finally {
      setLoading(false);
    }
  }, [exerciseId, classId, message]);

  useEffect(() => {
    fetchSceneData();
  }, [fetchSceneData]);

  // 监控streamingContents状态变化
  useEffect(() => {
    // streamingContents状态更新监控
  }, [streamingContents]);

  // 用于触发自动发声的状态
  const [pendingAutoVoice, setPendingAutoVoice] = useState<{id: number, content: string, character: CharacterInfo} | null>(null);

  // 处理单个任务
  const processJobTask = useCallback(async (job: JobInfo) => {
    if (processingJobs.has(job.id)) {
      return; // 任务已在处理中
    }

    // 检查任务是否已失败
    if (failedJobs.has(job.id)) {
      return;
    }

    // 检查重试次数
    const retryCount = jobRetryCount.get(job.id) || 0;
    const maxRetries = 0; // 最大重试次数（0表示不重试，但会执行一次）

    // 如果重试次数大于最大重试次数，则跳过（注意：第一次执行时retryCount为0，应该允许执行）
    if (retryCount > maxRetries) {
      setFailedJobs(prev => new Set(prev).add(job.id));
      return;
    }
    setProcessingJobs(prev => new Set(prev).add(job.id));
    // 标记角色为正在处理Job
    setProcessingCharacters(prev => new Set(prev).add(job.cid));

    let currentStreamContent = '';

    try {
      await processJob(job.id, {
        onChunk: (chunk: string) => {
          try {
            // 解析JSON数据
            const data = JSON.parse(chunk);

            // 处理OpenAI格式的流式响应
            if (data.choices && data.choices.length > 0 && data.choices[0].delta && data.choices[0].delta.content) {
              const content = data.choices[0].delta.content;
              currentStreamContent += content;
              setStreamingContents(prev => ({
                ...prev,
                [job.cid]: currentStreamContent
              }));
              // 流式内容更新时滚动到底部
              scrollToBottom();
            }
            // 兼容其他格式：直接包含content字段
            else if (data.content) {
              currentStreamContent += data.content;
              setStreamingContents(prev => ({
                ...prev,
                [job.cid]: currentStreamContent
              }));
              // 流式内容更新时滚动到底部
              scrollToBottom();
            }
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          } catch (error) {
            // 如果不是JSON格式，直接添加到流式内容
            currentStreamContent += chunk;
            setStreamingContents(prev => ({
              ...prev,
              [job.cid]: currentStreamContent
            }));
            // 流式内容更新时滚动到底部
            scrollToBottom();
          }
        },
        onComplete: () => {
          setProcessingJobs(prev => {
            const newSet = new Set(prev);
            newSet.delete(job.id);
            return newSet;
          });

          // 移除角色的处理Job状态
          setProcessingCharacters(prev => {
            const newSet = new Set(prev);
            newSet.delete(job.cid);
            return newSet;
          });

          // 任务成功完成，清除重试计数
          setJobRetryCount(prev => {
            const newMap = new Map(prev);
            newMap.delete(job.id);
            return newMap;
          });

          // 任务完成后，将流式内容添加到场景数据中
          if (currentStreamContent) {
            const newSpeechId = Date.now(); // 临时ID

            // 先更新场景数据
            setSceneData(prevData => {
              if (!prevData) return prevData;

              // 找到对应的角色
              const character = prevData.characters.find(c => c.id === job.cid);
              if (!character) return prevData;

              // 创建新的发言记录
              const newSpeech = {
                id: newSpeechId,
                cid: job.cid,
                played: 0, // AI扮演
                content: currentStreamContent,
                ctime: new Date().toISOString(),
              };

              // 标记新生成的AI发言，用于后续自动发声
              setPendingAutoVoice({
                id: newSpeechId,
                content: currentStreamContent,
                character: character
              });

              return {
                ...prevData,
                speeches: [...prevData.speeches, newSpeech]
              };
            });
          }

          // 清除流式内容
          setStreamingContents(prev => {
            const newContents = { ...prev };
            delete newContents[job.cid];
            return newContents;
          });
        },
        onError: (error: Error) => {
          console.error(`处理任务 ${job.id} 失败:`, error);
          setProcessingJobs(prev => {
            const newSet = new Set(prev);
            newSet.delete(job.id);
            return newSet;
          });

          // 移除角色的处理Job状态
          setProcessingCharacters(prev => {
            const newSet = new Set(prev);
            newSet.delete(job.cid);
            return newSet;
          });
          
          // 增加重试计数
          setJobRetryCount(prev => {
            const newMap = new Map(prev);
            newMap.set(job.id, retryCount + 1);
            return newMap;
          });
          
          // 清除流式内容
          setStreamingContents(prev => {
            const newContents = { ...prev };
            delete newContents[job.cid];
            return newContents;
          });
          
          // 如果达到最大重试次数，标记为失败
          if (retryCount + 1 > maxRetries) {
            setFailedJobs(prev => new Set(prev).add(job.id));
            message.error(`任务处理失败，已达到最大重试次数: ${error.message}`);
          } else {
            message.warning(`任务处理失败，将重试 (${retryCount + 1}/${maxRetries}): ${error.message}`);
          }
        }
      });
    } catch (error) {
      console.error(`处理任务 ${job.id} 失败:`, error);
      setProcessingJobs(prev => {
        const newSet = new Set(prev);
        newSet.delete(job.id);
        return newSet;
      });

      // 移除角色的处理Job状态
      setProcessingCharacters(prev => {
        const newSet = new Set(prev);
        newSet.delete(job.cid);
        return newSet;
      });
      
      // 增加重试计数
      setJobRetryCount(prev => {
        const newMap = new Map(prev);
        newMap.set(job.id, retryCount + 1);
        return newMap;
      });
      
      // 清除流式内容
      setStreamingContents(prev => {
        const newContents = { ...prev };
        delete newContents[job.cid];
        return newContents;
      });
      
      // 如果达到最大重试次数，标记为失败
      if (retryCount + 1 > maxRetries) {
        setFailedJobs(prev => new Set(prev).add(job.id));
        message.error("任务处理失败，已达到最大重试次数");
      } else {
        message.warning(`任务处理失败，将重试 (${retryCount + 1}/${maxRetries})`);
      }
    }
  }, [processingJobs, failedJobs, jobRetryCount, message, scrollToBottom]);

  // 按顺序处理任务列表
  const processJobsInOrder = useCallback(async (jobs: JobInfo[]) => {
    if (jobs.length === 0) return;

    // 按创建时间排序
    const sortedJobs = [...jobs].sort((a, b) => new Date(a.ctime).getTime() - new Date(b.ctime).getTime());

    // 用于跟踪正在执行的异步任务的Promise
    const runningAsyncTasks: Promise<void>[] = [];

    for (const job of sortedJobs) {
      if (job.sync === 1) {
        // 同步任务：等待前面所有任务（包括异步任务）完成后才执行
        if (runningAsyncTasks.length > 0) {
          await Promise.all(runningAsyncTasks);
          runningAsyncTasks.length = 0; // 清空已完成的异步任务
        }
        await processJobTask(job);
      } else {
        // 异步任务：直接开始处理，但将Promise添加到跟踪列表中
        const asyncTask = processJobTask(job);
        runningAsyncTasks.push(asyncTask);
      }
    }

    // 等待所有剩余的异步任务完成
    if (runningAsyncTasks.length > 0) {
      await Promise.all(runningAsyncTasks);
    }
  }, [processJobTask]);

  // 检查并处理未完成的任务
  const checkAndProcessPendingJobs = useCallback(async (speechId: number) => {
    const currentElid = sceneData?.elid;
    if (!currentElid) {
      console.error("缺少elid，无法检查任务");
      return;
    }
    
    // 防止重复处理同一个speechId
    if (lastProcessedSpeechIdRef.current === speechId) {
      return;
    }
    
    lastProcessedSpeechIdRef.current = speechId;
    
    try {
      const response = await getSpeechJobs(speechId, currentElid);
      if (response.jobs && response.jobs.length > 0) {
        // 过滤出status=0且未失败的任务
        setFailedJobs(currentFailedJobs => {
          const pendingJobs = response.jobs.filter(job => 
            job.status === 0 && !currentFailedJobs.has(job.id)
          );
          if (pendingJobs.length > 0) {
            processJobsInOrder(pendingJobs);
          } else {
            // 如果没有待处理任务，清除lastSpeechId以停止检查
            setLastSpeechId(null);
            lastProcessedSpeechIdRef.current = null; // 重置防抖标记
          }
          return currentFailedJobs; // 返回原状态，不修改
        });
      } else {
        // 没有任务时也重置防抖标记
        setLastSpeechId(null);
        lastProcessedSpeechIdRef.current = null;
      }
    } catch (error) {
      console.error("检查未完成任务失败:", error);
      lastProcessedSpeechIdRef.current = null; // 出错时重置防抖标记
    }
  }, [processJobsInOrder, sceneData?.elid]);

  // 页面初始化检查标记
  const initialCheckDoneRef = useRef(false);

  // 页面加载时检查未完成任务
  useEffect(() => {
    if (lastSpeechId && !initialCheckDoneRef.current) {
      checkAndProcessPendingJobs(lastSpeechId);
    }
  }, [lastSpeechId, checkAndProcessPendingJobs]);

  // Step I: 页面初始化时检查是否有未完成任务
  useEffect(() => {
    const checkInitialPendingJobs = async () => {
      if (!sceneData?.speeches || !sceneData?.elid || !processJobsInOrder || initialCheckDoneRef.current) return;
      
      // 获取最后一条发言的ID，检查是否有未完成的任务
      if (sceneData.speeches.length > 0) {
        const lastSpeech = sceneData.speeches[sceneData.speeches.length - 1];
        if (lastSpeech) {
          try {
            initialCheckDoneRef.current = true; // 标记已经执行过初始化检查
            
            const response = await getSpeechJobs(lastSpeech.id, sceneData.elid);
            if (response.jobs && response.jobs.length > 0) {
              // 过滤出status=0的未完成任务
              const pendingJobs = response.jobs.filter(job => job.status === 0);

              if (pendingJobs.length > 0) {
                // 设置lastSpeechId，但不会触发上面的useEffect因为initialCheckDoneRef.current已经是true
                setLastSpeechId(lastSpeech.id);
                // 直接处理未完成的任务
                processJobsInOrder(pendingJobs);
              }
            }
          } catch (error) {
            console.error("检查未完成任务失败:", error);
            initialCheckDoneRef.current = false; // 出错时重置标记，允许重试
          }
        }
      }
    };

    checkInitialPendingJobs();
  }, [sceneData?.speeches, sceneData?.elid, processJobsInOrder]);

  // 处理发送消息
  const handleSendMessage = async () => {
    if (!messageText.trim() || !selectedCharacter || !sceneData?.elid) {
      message.warning("请选择角色并输入发言内容");
      return;
    }

    try {
      setSending(true);
      
      // 构建指定发言的角色ID列表
      const toCids = selectedMentionCharacters.map(c => c.id).join(',');
      
      const request: CreateSpeechRequest = {
        elid: sceneData.elid,
        cid: selectedCharacter.id,
        played: 1, // 学员扮演
        content: messageText.trim(),
        to_cids: toCids, // 指定发言的角色ID列表
      };

      // Step 1: 保存发言
      const speechResponse = await createSpeech(request);
      const speechId = speechResponse.id;

      // 清空输入
      setMessageText("");
      setSelectedMentionCharacters([]); // 清空指定发言的角色列表

      // 立即更新UI显示用户发言
      setSceneData(prevData => {
        if (!prevData) return prevData;

        const newSpeech = {
          id: speechId,
          cid: selectedCharacter.id,
          played: 1,
          content: request.content,
          to_cids: request.to_cids || null, // 包含@提及的角色ID列表
          ctime: new Date().toISOString(),
        };

        return {
          ...prevData,
          speeches: [...prevData.speeches, newSpeech]
        };
      });

      // 显示等待参会者发言状态
      setWaitingForParticipants(true);

      // Step 2: 创建任务
      const jobsResponse = await createSpeechJobs(speechId, sceneData.elid);
      
      // 隐藏等待参会者发言状态
      setWaitingForParticipants(false);
      
      // Step 3: 处理任务
      // 清理之前的失败状态
      setFailedJobs(new Set());
      setJobRetryCount(new Map());
      lastProcessedSpeechIdRef.current = null; // 重置防抖标记，允许处理新的speechId
      setLastSpeechId(speechId);
      
      // 立即处理创建的任务列表
      if (jobsResponse.jobs && jobsResponse.jobs.length > 0) {
        await processJobsInOrder(jobsResponse.jobs);
      }


    } catch (error) {
      console.error("发言失败:", error);
      message.error("发言失败，请重试");
      // 出错时也要隐藏等待状态
      setWaitingForParticipants(false);
    } finally {
      setSending(false);
    }
  };

  // 手动重试失败的任务
  const handleRetryFailedJobs = useCallback(async () => {
    if (!lastSpeechId || !sceneData?.elid) {
      message.warning("没有可重试的任务");
      return;
    }

    try {
      // 清除失败状态，允许重新处理
      setFailedJobs(new Set());
      setJobRetryCount(new Map());
      lastProcessedSpeechIdRef.current = null; // 重置防抖标记，允许重试
      
      // 重新检查并处理任务
      await checkAndProcessPendingJobs(lastSpeechId);
      message.success("开始重试失败的任务");
    } catch (error) {
      console.error("重试失败:", error);
      message.error("重试失败，请稍后再试");
    }
  }, [lastSpeechId, sceneData?.elid, checkAndProcessPendingJobs, message]);

  // 处理提交作业
  const handleSubmitExercise = useCallback(async () => {
    if (isCommenting) {
      message.warning("请等待当前点评结束！");
      return;
    }

    if (!sceneData?.elid) {
      message.error("缺少必要信息，无法提交作业");
      return;
    }

    try {
      setSubmitting(true);

      await updateExerciseLogStatus(sceneData.elid, 2);

      // 更新本地状态
      setSceneData((prev) =>
        prev
          ? {
              ...prev,
              status: 2,
              stime: new Date().toISOString(),
            }
          : null
      );

      message.success("练习提交成功");
    } catch (error) {
      console.error("提交作业失败:", error);
      message.error("提交作业失败，请重试");
    } finally {
      setSubmitting(false);
    }
  }, [sceneData?.elid, message, isCommenting]);

  // 处理撤销提交
  const handleCancelSubmit = useCallback(async () => {
    if (isCommenting) {
      message.warning("请等待当前点评结束！");
      return;
    }

    if (!sceneData?.elid) {
      message.error("缺少必要信息，无法撤销提交");
      return;
    }

    try {
      setSubmitting(true);

      await updateExerciseLogStatus(sceneData.elid, 1);

      // 更新本地状态
      setSceneData((prev) =>
        prev
          ? {
              ...prev,
              status: 1,
              stime: null,
            }
          : null
      );

      message.success("撤销提交成功");
    } catch (error) {
      console.error("撤销提交失败:", error);
      message.error("撤销提交失败，请重试");
    } finally {
      setSubmitting(false);
    }
  }, [sceneData?.elid, message, isCommenting]);

  // 获取状态标签
  const getStatusTag = (status: number) => {
    switch (status) {
      case 0:
        return (
          <Tag color="#8c8c8c" className="status-tag status-tag-pending">
            待练习
          </Tag>
        );
      case 1:
        return (
          <Tag color="#21808d" className="status-tag status-tag-in-progress">
            练习中
          </Tag>
        );
      case 2:
        return (
          <Tag color="#13343b" className="status-tag status-tag-submitted">
            已提交
          </Tag>
        );
      default:
        return (
          <Tag color="#8c8c8c" className="status-tag status-tag-pending">
            未开始
          </Tag>
        );
    }
  };

  // 获取老师信息（从本地存储）
  const getTeacherInfoFromStorage = useCallback(() => {
    if (sceneData?.tid) {
      return getTeacherInfo(sceneData.tid);
    }
    return null;
  }, [sceneData?.tid]);

  // 渲染老师信息
  const renderTeacherInfo = () => {
    const teacherInfo = getTeacherInfoFromStorage();
    if (!teacherInfo && !sceneData?.tname) return null;

    const name = teacherInfo?.tname || sceneData?.tname;
    const avatarSrc = sceneData?.tid ? getTeacherAvatar(sceneData.tid) : null;

    return (
      <div style={{ display: "flex", alignItems: "center" }}>
        {avatarSrc ? (
          <img
            src={avatarSrc}
            alt="老师头像"
            className="teacher-avatar"
            onError={(e) => {
              // 头像加载失败时显示默认图标
              const target = e.target as HTMLImageElement;
              target.style.display = "none";
              const icon = target.nextElementSibling as HTMLElement;
              if (icon) icon.style.display = "inline";
            }}
          />
        ) : null}
        <UserOutlined
          className="teacher-icon"
          style={{ display: avatarSrc ? "none" : "inline" }}
        />
        <Text type="secondary" className="teacher-text">
          指导老师: {name}
        </Text>
      </div>
    );
  };

  // 格式化时间显示
  const formatTime = useCallback((timeString: string) => {
    const date = new Date(timeString);
    return date.toLocaleString("zh-CN", {
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  }, []);

  // 获取性别显示文本

  // 获取角色头像
  const getCharacterAvatar = (character: CharacterInfo) => {
    if (character.avatar) {
      return <Avatar src={character.avatar} size={40} />;
    }
    return <Avatar icon={<UserOutlined />} size={40} />;
  };

  // 处理练习指南抽屉
  const handleShowGuide = () => {
    setGuideDrawerVisible(true);
  };

  // 处理全屏切换
  const handleToggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    setFullscreenTooltipOpen(false); // 点击后关闭tooltip
  };

  // 处理显示角色profile
  const handleShowProfile = (character: CharacterInfo, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发角色选择
    setSelectedCharacterForProfile(character);
    setProfileModalVisible(true);
  };

  // 获取随机的发言状态文本
  const getRandomSpeakingText = () => {
    const speakingTexts = [
      "思索中",
      "组织语言中",
      "酝酿中",
      "整理思路中",
      "思考中"
    ];
    const randomIndex = Math.floor(Math.random() * speakingTexts.length);
    return speakingTexts[randomIndex];
  };

  // 根据发言者获取角色信息
  const getCharacterById = (cid: number) => {
    return sceneData?.characters.find((c) => c.id === cid);
  };

  // 获取@提及的用户名列表
  const getMentionedCharacterNames = (toCids?: string | null) => {
    if (!toCids || !sceneData?.characters) return [];

    const cidList = toCids.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
    return cidList.map(cid => {
      const character = sceneData.characters.find(c => c.id === cid);
      return character ? character.name : null;
    }).filter(name => name !== null) as string[];
  };

  // 检查是否有任务正在处理
  const hasProcessingTasks = useCallback(() => {
    return processingJobs.size > 0 || Object.keys(streamingContents).length > 0;
  }, [processingJobs.size, streamingContents]);



  // 处理AI老师点评
  const handleAIComment = async () => {
    // 检查是否有任务正在处理
    if (hasProcessingTasks()) {
      message.warning("请等待当前发言结束！");
      return;
    }

    if (!sceneData?.elid) {
      message.error("缺少必要信息，无法生成点评");
      return;
    }

    try {
      setIsCommenting(true);
      setStreamingComments('');
      setShowCommentsPanel(true); // 开始生成时显示面板

      let fullComments = '';

      const request: SceneCommentsRequest = {
        elid: sceneData.elid
      };

      await createSceneComments(request, {
        onChunk: (chunk: string) => {
          try {
            // 解析JSON数据
            const data = JSON.parse(chunk);

            // 处理OpenAI格式的流式响应
            if (data.choices && data.choices.length > 0 && data.choices[0].delta && data.choices[0].delta.content) {
              const content = data.choices[0].delta.content;
              fullComments += content;
              setStreamingComments(fullComments);
              scrollCommentsToBottom();
            }
            // 兼容其他格式：直接包含content字段
            else if (data.content) {
              fullComments += data.content;
              setStreamingComments(fullComments);
              scrollCommentsToBottom();
            }
          } catch {
            // 如果不是JSON格式，直接添加到流式内容
            fullComments += chunk;
            setStreamingComments(fullComments);
            scrollCommentsToBottom();
          }
        },
        onComplete: () => {
          setIsCommenting(false);
          setHasAIComment(true);
          setShowCommentsPanel(true); // 自动显示点评面板
          // 更新场景数据中的comments字段
          setSceneData(prevData => {
            if (!prevData) return prevData;
            return {
              ...prevData,
              comments: fullComments
            };
          });
          message.success("AI老师点评完成");
        },
        onError: (error: Error) => {
          console.error("生成AI点评失败:", error);
          setIsCommenting(false);
          setStreamingComments('');
          setShowCommentsPanel(false);
          message.error(`生成AI点评失败: ${error.message}`);
        }
      });
    } catch (error) {
      console.error("生成AI点评失败:", error);
      setIsCommenting(false);
      setStreamingComments('');
      setShowCommentsPanel(false);
      message.error("生成AI点评失败，请重试");
    }
  };

  // 处理重新练习
  const handleRetry = async () => {
    if (!sceneData?.elid) {
      message.error("缺少必要信息，无法重新练习");
      return;
    }

    try {
      const request: SceneCommentsRequest = {
        elid: sceneData.elid
      };

      await deleteSceneComments(request);

      setHasAIComment(false);
      setShowCommentsPanel(false);
      setStreamingComments('');

      // 更新场景数据中的comments字段
      setSceneData(prevData => {
        if (!prevData) return prevData;
        return {
          ...prevData,
          comments: null
        };
      });

      message.success("重新开始练习");
    } catch (error) {
      console.error("重新练习失败:", error);
      message.error("重新练习失败，请重试");
    }
  };

  // 处理显示点评内容
  const handleShowComments = () => {
    setShowCommentsPanel(true);
  };

  // 处理隐藏点评内容
  const handleHideComments = () => {
    if (isCommenting) {
      message.warning("请等待当前点评结束！");
      return;
    }
    setShowCommentsPanel(false);
  };

  // 获取tooltip文本
  const getTooltipText = () => {
    return hasAIComment ? "想再次练习，请点击重新练习按钮！" : "";
  };

  // 获取placeholder文本
  const getPlaceholderText = () => {
    if (hasAIComment) {
      return "想再次练习，请点击重新练习按钮！";
    }
    if (selectedCharacter) {
      return `请以【${selectedCharacter.name}】的身份发言`;
    }
    return "请先选择角色";
  };

  // 处理复制消息
  const handleCopyMessage = useCallback(async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      message.success("消息已复制到剪贴板");
    } catch (error) {
      console.error("复制失败:", error);
      message.error("复制失败，请重试");
    }
  }, [message]);

  // 处理文字转语音
  const handleTextToSpeech = useCallback(async (speechId: number, content: string, character?: CharacterInfo) => {
    try {
      // 立即设置为准备中状态
      setPlayingSpeechId(speechId);
      setTtsStatus('connecting');

      // 设置TTS服务回调
      ttsService.setStatusChangeCallback((status: TTSStatus) => {
        if (status === TTSStatus.PLAYING) {
          // 只有真正开始播放时才设置为playing状态
          setTtsStatus('playing');
        } else if (status === TTSStatus.COMPLETED || status === TTSStatus.ERROR) {
          // 播放完成或出错后，直接恢复到默认状态
          setPlayingSpeechId(null);
          setTtsStatus('idle');
        }
        // 其他状态（CONNECTING, CONNECTED）保持connecting状态不变
      });

      ttsService.setErrorCallback((error: string) => {
        console.error("TTS错误:", error);
        message.error(`语音播放失败: ${error}`);
        setPlayingSpeechId(null);
        setTtsStatus('idle');
      });

      ttsService.setCompleteCallback(() => {
        // TTS播放完成
      });

      // 构建TTS请求
      const ttsRequest = {
        text: content,
        timbre: character?.timbre || "zh_female_shuangkuaisisi_moon_bigtts", // 使用角色音色或默认音色
        speed_ratio: 1.0,
        volume_ratio: 1.0,
        pitch_ratio: 1.0,
        encoding: "mp3"
      };

      // 发送TTS请求
      await ttsService.synthesize(ttsRequest);

    } catch (error) {
      console.error("文字转语音失败:", error);
      message.error("语音播放失败，请重试");
      setPlayingSpeechId(null);
      setTtsStatus('idle');
    }
  }, [message]);

  // 停止语音播放
  const handleStopTTS = useCallback(() => {
    ttsService.stop();
    // 手动停止时直接恢复到默认状态
    setPlayingSpeechId(null);
    setTtsStatus('idle');
  }, []);

  // 监听新生成的AI发言并自动发声
  useEffect(() => {
    if (pendingAutoVoice && autoVoice && !playingSpeechId && ttsStatus === 'idle') {
      const { id, content, character } = pendingAutoVoice;

      // 清除标记，避免重复播放
      setPendingAutoVoice(null);

      // 延迟一点时间确保状态稳定
      setTimeout(() => {
        if (!playingSpeechId && ttsStatus === 'idle') {
          handleTextToSpeech(id, content, character);
        }
      }, 100);
    }
  }, [pendingAutoVoice, autoVoice, playingSpeechId, ttsStatus, handleTextToSpeech]);





  // 处理删除消息
  const handleDeleteMessage = useCallback((speechId: number) => {
    modal.confirm({
      title: "删除确认",
      content: "您确定删除此消息及其后所有消息吗？",
      footer: [
        <div key="footer" style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px', marginTop: "16px"}}>
          <Button className="custom-button" onClick={() => {
            Modal.destroyAll();
          }}>
            取消
          </Button>
          <Button 
            className="save-button delete-button"
            onClick={async () => {
              Modal.destroyAll();
              
              try {
                // 调用 DELETE /api/v1/scene/speech/batch 接口
                if (sceneData?.elid) {
                  // 找到要删除的消息的索引
                  const speechIndex = sceneData.speeches.findIndex(speech => speech.id === speechId);
                  if (speechIndex === -1) {
                    message.error("未找到要删除的消息");
                    return;
                  }
                  
                  // 调用批量删除接口
                  await deleteSpeechBatch({
                    from_id: speechId,
                    elid: sceneData.elid
                  });
                  
                  // 从本地状态中删除消息
                  setSceneData(prevData => {
                    if (!prevData) return prevData;
                    
                    return {
                      ...prevData,
                      speeches: prevData.speeches.slice(0, speechIndex)
                    };
                  });
                  
                  message.success("消息已删除");
                } else {
                  message.error("练习ID不存在，无法删除消息");
                }
              } catch (error) {
                console.error('删除消息失败:', error);
                message.error("删除消息失败，请重试");
              }
            }}
          >
            删除
          </Button>
        </div>
      ],
      keyboard: true,
      maskClosable: true
    });
  }, [modal, message, sceneData]);

  // 处理指定发言 - 添加角色到指定列表
  const handleAddMentionCharacter = (character: CharacterInfo) => {
    if (!selectedMentionCharacters.find(c => c.id === character.id)) {
      setSelectedMentionCharacters(prev => [...prev, character]);
    }
  };

  // 处理指定发言 - 添加所有角色
  const handleAddAllCharacters = () => {
    if (sceneData?.characters) {
      // 获取可以@的角色：排除played=1的角色（已被学员扮演的角色）和当前选中的角色（不能@自己）
      const availableCharacters = sceneData.characters.filter(c =>
        // 排除已被学员扮演的角色
        c.played !== 1 &&
        // 排除当前选中的角色（不能@自己）
        c.id !== selectedCharacter?.id
      );
      setSelectedMentionCharacters(availableCharacters);
    }
  };

  // 处理指定发言 - 移除角色
  const handleRemoveMentionCharacter = (characterId: number) => {
    setSelectedMentionCharacters(prev => prev.filter(c => c.id !== characterId));
  };

  // 处理指定发言 - 清除所有角色
  const handleClearAllMentionCharacters = () => {
    setSelectedMentionCharacters([]);
  };

  // 获取指定发言的下拉菜单项
  const getMentionMenuItems = (): MenuProps['items'] => {
    if (!sceneData?.characters) return [];

    // 获取可以@的角色：排除played=1的角色（已被学员扮演的角色）和当前选中的角色（不能@自己）
    const availableCharacters = sceneData.characters.filter(c =>
      // 排除已被学员扮演的角色
      c.played !== 1 &&
      // 排除当前选中的角色（不能@自己）
      c.id !== selectedCharacter?.id
    );

    const items: MenuProps['items'] = [
      {
        key: 'all',
        label: (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Avatar size={24} style={{ backgroundColor: '#1890ff' }} icon={<UserOutlined />} />
            <span>所有人</span>
          </div>
        ),
        onClick: handleAddAllCharacters,
      },
      ...(availableCharacters.length > 0 ? [{ type: 'divider' as const }] : []),
      ...availableCharacters.map(character => ({
        key: character.id,
        label: (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {character.avatar ? (
              <Avatar src={character.avatar} size={24} />
            ) : (
              <Avatar size={24} icon={<UserOutlined />} />
            )}
            <span>{character.name}</span>
          </div>
        ),
        onClick: () => handleAddMentionCharacter(character),
      }))
    ];

    return items;
  };

  return (
    <MainLayout>
      <div
        style={{
          height: "calc(100vh - 48px)",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {loading ? (
          <div className="scene-loading-container">
            <Spin size="large" />
            <div className="scene-loading-text">
              <Text type="secondary">正在加载场景信息...</Text>
            </div>
          </div>
        ) : !sceneData ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无场景信息"
            style={{ padding: "50px" }}
          />
        ) : (
          <div style={{ height: "100%", display: "flex", flexDirection: "column" }}>
            {/* 顶部信息栏 */}
            {!isFullscreen && (
            <div className="scene-header">
              <div className="scene-header-content">
                <div>
                  <div className="scene-header-info">
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "16px",
                      }}
                    >
                      <Title
                        level={2}
                        className="scene-title"
                        style={{ margin: 0 }}
                      >
                        {sceneData.title}
                      </Title>
                      {/* 提交作业按钮 */}
                      {sceneData.status === 2 ? (
                        <Button
                          type="primary"
                          danger
                          loading={submitting}
                          onClick={() => {
                            modal.confirm({
                              title: "撤销提交",
                              content: "确定要撤销提交吗？撤销后可以继续练习。",
                              onOk: handleCancelSubmit,
                            });
                          }}
                          style={{
                            borderRadius: "8px",
                            fontWeight: "500",
                          }}
                        >
                          撤销提交
                        </Button>
                      ) : (
                        <Button
                          type="primary"
                          loading={submitting}
                          onClick={() => {
                            modal.confirm({
                              title: "提交作业",
                              content:
                                "确定要提交作业吗？提交后将无法继续练习。",
                              onOk: handleSubmitExercise,
                            });
                          }}
                          style={{
                            borderRadius: "8px",
                            fontWeight: "500",
                            background: "#21808d",
                            borderColor: "#21808d",
                          }}
                        >
                          提交作业
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="scene-header-meta">
                    {getStatusTag(sceneData.status)}

                    {renderTeacherInfo()}

                    {sceneData.duration && (
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <ClockCircleOutlined className="info-icon" />
                        <Text type="secondary" className="info-text">
                          预计时长: {sceneData.duration} 分钟
                        </Text>
                      </div>
                    )}
                  </div>
                </div>

                <Tooltip title="返回" placement="left">
                  <Button
                    type="text"
                    icon={<RollbackOutlined />}
                    onClick={() => {
                      // 检查是否有任务正在处理
                      if (hasProcessingTasks()) {
                        message.warning("请等待当前发言结束！");
                        return;
                      }
                      // 检查是否正在生成点评
                      if (isCommenting) {
                        message.warning("请等待当前点评结束！");
                        return;
                      }
                      navigate(`/class/${classId}/exercises`);
                    }}
                    style={{
                      width: "48px",
                      height: "48px",
                      borderRadius: "12px",
                      background: "rgba(255, 255, 255, 0.9)",
                      boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  />
                </Tooltip>
              </div>
            </div>
            )}

            {/* 下半部分 - 撑满剩余高度 */}
            <Layout className="scene-layout" style={{
              boxShadow: '0 4px 12px rgba(38, 38, 38, 0.15)',
              borderRadius: '12px',
              overflow: 'hidden',
              height: isFullscreen ? '100vh' : 'auto'
            }}>
              {collapsed && (
                <Button
                  type="text"
                  icon={<MenuUnfoldOutlined />}
                  onClick={() => {
                    if (isCommenting) {
                      message.warning("请等待当前点评结束！");
                      return;
                    }
                    setCollapsed(false);
                  }}
                  className="scene-unfold-button"
                />
              )}

              {/* 左侧角色列表 */}
              <Sider
                collapsible
                collapsed={collapsed}
                onCollapse={setCollapsed}
                width={240}
                className="scene-character-sider"
                breakpoint="lg"
                collapsedWidth={0}
                trigger={null}
              >
                <div className="scene-character-list-container" style={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  {/* 顶部标题和收起展开按钮 */}
                  <div className="scene-character-header">
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div className="scene-character-header-left">
                        <span>参会者</span>
                      </div>
                      <div className="scene-character-header-right">
                        {!collapsed && (
                          <Button
                            type="text"
                            icon={<MenuFoldOutlined />}
                            onClick={() => {
                              if (isCommenting) {
                                message.warning("请等待当前点评结束！");
                                return;
                              }
                              setCollapsed(true);
                            }}
                            className="scene-character-fold-button"
                          />
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <List
                    dataSource={sceneData.characters}
                    style={{ flex: 1, overflow: 'auto' }}
                    renderItem={(character) => (
                      <List.Item
                        className="scene-character-item"
                      >
                        <div className="scene-character-item-content">
                          <div className="scene-character-left">
                            <Tooltip title="点击查看个人资料">
                              <div onClick={(e) => handleShowProfile(character, e)} style={{ cursor: 'pointer' }}>
                                {getCharacterAvatar(character)}
                              </div>
                            </Tooltip>
                            <div className="scene-character-name-group">
                              <span>{character.name}</span>
                            </div>
                          </div>
                          <div className="scene-character-status-icon">
                            {/* 根据角色的played字段显示状态图标 */}
                            {character.played === 1 ? (
                              <Tooltip title="学员扮演">
                                <UserOutlined
                                  onClick={(e) => handleShowProfile(character, e)}
                                  className="scene-character-user-icon"
                                  style={{ cursor: 'pointer' }}
                                />
                              </Tooltip>
                            ) : (
                              <Tooltip title="AI陪练机器人">
                                <RobotOutlined
                                  onClick={(e) => handleShowProfile(character, e)}
                                  className="scene-character-robot-icon"
                                  style={{ cursor: 'pointer' }}
                                />
                              </Tooltip>
                            )}
                          </div>
                        </div>
                      </List.Item>
                    )}
                  />
                  
                  {/* AI老师点评按钮 - 移动到参会者列表底部 */}
                  <div className="scene-character-footer" style={{
                    padding: '16px',
                    borderTop: '1px solid #f0f0f0',
                    marginTop: 'auto',
                    display: 'flex',
                    justifyContent: 'center',
                    gap: '8px'
                  }}>
                    {!hasAIComment ? (
                      <Button
                        icon={
                          sceneData?.tid && getTeacherAvatar(sceneData.tid) ? (
                            <Avatar
                              src={getTeacherAvatar(sceneData.tid)}
                              size={20}
                            />
                          ) : (
                            <UserOutlined style={{ verticalAlign: 'middle' }} />
                          )
                        }
                        onClick={handleAIComment}
                        className="ai-comment-button"
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px',
                          fontSize: '16px'
                        }}
                        disabled={sceneData?.status === 2 || isCommenting}
                        loading={isCommenting}
                      >
                        AI老师点评
                      </Button>
                    ) : (
                      <>
                        <Button
                          type="primary"
                          icon={<RedoOutlined />}
                          onClick={handleRetry}
                          className="retry-button"
                          disabled={sceneData?.status === 2}
                        >
                          重新练习
                        </Button>
                        <Tooltip
                          title="点评内容"
                          mouseEnterDelay={1}
                        >
                          <Button
                            onClick={handleShowComments}
                            className="ai-comment-button"
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '6px',
                              fontSize: '16px'
                            }}
                          >
                            {sceneData?.tid && getTeacherAvatar(sceneData.tid) ? (
                              <Avatar
                                src={getTeacherAvatar(sceneData.tid)}
                                size={20}
                              />
                            ) : (
                              <UserOutlined style={{ verticalAlign: 'middle' }} />
                            )}
                            <CommentOutlined />
                          </Button>
                        </Tooltip>
                      </>
                    )}
                  </div>
                </div>
              </Sider>

              {/* 右侧聊天区域 */}
              <Layout className="scene-chat-layout" style={{ position: 'relative' }}>
                {/* AI老师点评悬浮面板 */}
                {showCommentsPanel && (hasAIComment || isCommenting) && (
                  <div className="scene-comments-overlay" onClick={handleHideComments}>
                    <div className="scene-comments-panel" onClick={(e) => e.stopPropagation()}>
                      <div className="scene-comments-header">
                        <div className="scene-comments-title">
                          {sceneData?.tid && getTeacherAvatar(sceneData.tid) ? (
                            <Avatar
                              src={getTeacherAvatar(sceneData.tid)}
                              size={24}
                            />
                          ) : (
                            <UserOutlined />
                          )}
                          <span>AI老师点评</span>
                          {isCommenting && (
                            <EditOutlined className="writing-animation" />
                          )}
                        </div>
                        <Button
                          type="text"
                          icon={<CloseOutlined />}
                          onClick={handleHideComments}
                          size="small"
                        />
                      </div>
                      <div ref={commentsContentRef} className="scene-comments-content">
                        {isCommenting ? (
                          <StreamingMarkdownRenderer
                            content={streamingComments}
                            isStreaming={true}
                            className="comments-content"
                          />
                        ) : (
                          <MarkdownRenderer
                            content={sceneData?.comments || ''}
                            className="comments-content"
                          />
                        )}
                      </div>
                    </div>
                  </div>
                )}
                {/* 聊天区域顶部 */}
                <div className="scene-chat-header">
                  <div className="scene-chat-header-left">
                    {/* 重试失败任务按钮 */}
                    {failedJobs.size > 0 && (
                      <Button
                        type="text"
                        icon={<RedoOutlined />}
                        onClick={handleRetryFailedJobs}
                        className="scene-retry-button"
                        style={{
                          color: '#ff4d4f',
                          borderColor: '#ff4d4f'
                        }}
                      >
                        重试失败任务 ({failedJobs.size})
                      </Button>
                    )}
                  </div>

                  <div className="scene-chat-header-right">
                    {/* 练习指南按钮 */}
                    <Button
                      type="text"
                      icon={<CompassOutlined />}
                      onClick={handleShowGuide}
                      className="scene-guide-button"
                    >
                      练习指南
                    </Button>

                    {/* 自动发声/手动发声开关 */}
                    <div className="scene-voice-switch-container">
                      <Tooltip title="机器人发声方式">
                        <span className="scene-voice-icon">
                          {autoVoice ? <SoundFilled /> : <SoundOutlined />}
                        </span>
                      </Tooltip>
                      <Switch
                        checked={autoVoice}
                        onChange={setAutoVoice}
                        checkedChildren="自动发声"
                        unCheckedChildren="手动发声"
                        className="scene-voice-switch"
                      />
                    </div>

                    {/* 全屏/退出全屏按钮 */}
                    <Tooltip 
                      title={isFullscreen ? "退出全屏" : "全屏"} 
                      placement="top"
                      open={fullscreenTooltipOpen}
                      onOpenChange={setFullscreenTooltipOpen}
                    >
                      <Button
                        type="text"
                        icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                        onClick={handleToggleFullscreen}
                        className="scene-guide-button"
                      />
                    </Tooltip>
                  </div>
                </div>

                {/* 聊天消息列表 */}
                <Content
                  ref={chatContentRef}
                  className={`scene-chat-content ${showCommentsPanel ? 'with-comments-panel' : ''}`}
                  style={{ position: 'relative' }}
                >
                  {sceneData.speeches.length === 0 ? (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description="暂无发言记录，开始你的角色扮演吧！"
                      className="scene-chat-empty"
                    />
                  ) : (
                    <>
                      <AutoHideScrollbar
                        ref={scrollContainerRef}
                        style={{ height: '100%'}}
                      >
                        <div className="scene-messages-container">
                          {sceneData.speeches.map((speech) => {
                            const character = getCharacterById(speech.cid);
                            const isUserSpeech = speech.played === 1;

                            return (
                              <div
                                key={speech.id}
                                className={`scene-message-item ${
                                  isUserSpeech ? "user" : "ai"
                                }`}
                              >
                                <div
                                  className={`scene-message-wrapper ${
                                    isUserSpeech ? "user" : "ai"
                                  }`}
                                >
                                  {/* 消息头部：重构为两个控件的布局 */}
                                  <div className={`scene-message-header ${isUserSpeech ? "user" : "ai"}`}>
                                    {/* 用户消息：功能按钮控件在左侧 */}
                                    {isUserSpeech && (
                                      <div className="scene-message-actions-widget">
                                        <Tooltip title="复制" mouseEnterDelay={1}>
                                          <Button
                                            type="text"
                                            size="small"
                                            icon={<CopyOutlined />}
                                            onClick={() => handleCopyMessage(speech.content)}
                                            className="scene-message-action-btn"
                                          />
                                        </Tooltip>
                                        <Tooltip title="删除" mouseEnterDelay={1}>
                                          <Button
                                            type="text"
                                            size="small"
                                            icon={<DeleteOutlined />}
                                            onClick={() => handleDeleteMessage(speech.id)}
                                            className="scene-message-action-btn scene-message-delete-btn"
                                          />
                                        </Tooltip>
                                      </div>
                                    )}
                                    
                                    {/* AI消息：信息控件在左侧 */}
                                    {!isUserSpeech && (
                                      <div className={`scene-message-info-widget ${isUserSpeech ? "user" : "ai"}`}>
                                        <div className="scene-message-avatar">
                                          {character && (
                                            character.avatar ? (
                                              <Avatar src={character.avatar} size={32} />
                                            ) : (
                                              <Avatar icon={<UserOutlined />} size={32} />
                                            )
                                          )}
                                        </div>
                                        <div className={`scene-message-info ${isUserSpeech ? "user" : "ai"}`}>
                                          <div className="scene-message-name">
                                            {character?.name}{isUserSpeech ? "(你)" : ""}
                                          </div>
                                          <div className="scene-message-time">
                                            {formatTime(speech.ctime)}
                                          </div>
                                        </div>

                                        {/* AI消息的发声按钮区域 - 始终显示 */}
                                        {!isUserSpeech && (
                                          <div className="scene-message-tts-area">
                                            <div className="tts-status-container">
                                              {/* 发声按钮 - 始终显示，根据状态变化样式和行为 */}
                                              <Tooltip
                                                title={
                                                  playingSpeechId === speech.id && ttsStatus === 'connecting' ? "准备中" :
                                                  playingSpeechId === speech.id && ttsStatus === 'playing' ? "点击结束发声" :
                                                  "发声"
                                                }
                                                mouseEnterDelay={1}
                                              >
                                                <Button
                                                  type="text"
                                                  size="small"
                                                  icon={<SoundOutlined />}
                                                  onClick={() => {
                                                    if (playingSpeechId === speech.id && ttsStatus === 'playing') {
                                                      handleStopTTS();
                                                    } else if (playingSpeechId !== speech.id || ttsStatus === 'idle') {
                                                      handleTextToSpeech(speech.id, speech.content, character);
                                                    }
                                                  }}
                                                  disabled={playingSpeechId === speech.id && ttsStatus === 'connecting'}
                                                  className={`scene-message-action-btn scene-message-tts-btn ${
                                                    playingSpeechId === speech.id && ttsStatus === 'connecting' ? 'tts-preparing' :
                                                    playingSpeechId === speech.id && ttsStatus === 'playing' ? 'tts-playing' : ''
                                                  }`}
                                                />
                                              </Tooltip>

                                              {/* 准备中状态显示文字 */}
                                              {playingSpeechId === speech.id && ttsStatus === 'connecting' && (
                                                <span className="tts-status-text">准备中</span>
                                              )}

                                              {/* 发声中状态显示声纹效果 - 优化为5个bar的对称设计 */}
                                              {playingSpeechId === speech.id && ttsStatus === 'playing' && (
                                                <span className="voice-wave">
                                                  <span className="wave-bar"></span>
                                                  <span className="wave-bar"></span>
                                                  <span className="wave-bar"></span>
                                                  <span className="wave-bar"></span>
                                                  <span className="wave-bar"></span>
                                                </span>
                                              )}
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    )}
                                    
                                    {/* 用户消息：信息控件在右侧 */}
                                    {isUserSpeech && (
                                      <div className={`scene-message-info-widget ${isUserSpeech ? "user" : "ai"}`}>
                                        <div className="scene-message-avatar">
                                          {character && (
                                            character.avatar ? (
                                              <Avatar src={character.avatar} size={32} />
                                            ) : (
                                              <Avatar icon={<UserOutlined />} size={32} />
                                            )
                                          )}
                                        </div>
                                        <div className={`scene-message-info ${isUserSpeech ? "user" : "ai"}`}>
                                          <div className="scene-message-name">
                                            {character?.name}{isUserSpeech ? "(你)" : ""}
                                          </div>
                                          <div className="scene-message-time">
                                            {formatTime(speech.ctime)}
                                          </div>
                                        </div>
                                      </div>
                                    )}
                                    
                                    {/* AI消息：功能按钮控件在右侧 */}
                                    {!isUserSpeech && (
                                      <div className="scene-message-actions-widget">
                                        <Tooltip title="复制" mouseEnterDelay={1}>
                                          <Button
                                            type="text"
                                            size="small"
                                            icon={<CopyOutlined />}
                                            onClick={() => handleCopyMessage(speech.content)}
                                            className="scene-message-action-btn"
                                          />
                                        </Tooltip>

                                      </div>
                                    )}
                                  </div>

                                  {/* 消息内容气泡 */}
                                  <div
                                    className={`scene-message-bubble ${
                                      isUserSpeech ? "user" : "ai"
                                    }`}
                                  >
                                    {/* 显示@提及的用户 */}
                                    {speech.to_cids && (() => {
                                      const mentionedNames = getMentionedCharacterNames(speech.to_cids);
                                      if (mentionedNames.length > 0) {
                                        return (
                                          <div style={{
                                            marginBottom: '8px',
                                            fontSize: '14px',
                                            opacity: 0.8,
                                            color: isUserSpeech ? '#fff' : '#666'
                                          }}>
                                            {mentionedNames.map(name => `@${name}`).join(' ')}
                                          </div>
                                        );
                                      }
                                      return null;
                                    })()}
                                    <MarkdownRenderer
                                      content={speech.content}
                                      className={isUserSpeech ? "user-message-content" : "ai-message-content"}
                                    />
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                          
                          {/* 显示正在发言中的角色气泡（只有当开始处理Job时才显示） */}
                          {Array.from(processingCharacters).map(cid => {
                            const character = getCharacterById(cid);
                            if (!character) return null;

                            // 检查是否有流式内容
                            const streamingContent = streamingContents[cid];
                            const hasStreamingContent = streamingContent && streamingContent.trim().length > 0;

                            return (
                              <div
                                key={`speaking-${cid}`}
                                className="scene-message-item ai"
                              >
                                <div className="scene-message-wrapper ai">
                                  {/* 消息头部：头像、姓名、时间 */}
                                  <div className="scene-message-header ai">
                                    <div className="scene-message-info-widget ai">
                                      <div className="scene-message-avatar">
                                        {character.avatar ? (
                                          <Avatar src={character.avatar} size={32} />
                                        ) : (
                                          <Avatar icon={<UserOutlined />} size={32} />
                                        )}
                                      </div>
                                      <div className="scene-message-info ai">
                                        <div className="scene-message-name">
                                          {character.name}
                                        </div>
                                        <div className="scene-message-time">
                                          {hasStreamingContent ? "正在发言..." : "正在准备..."}
                                        </div>
                                      </div>
                                    </div>
                                  </div>

                                  {/* 消息内容气泡 */}
                                  <div className="scene-message-bubble ai">
                                    {hasStreamingContent ? (
                                      <StreamingMarkdownRenderer
                                        content={streamingContent}
                                        isStreaming={true}
                                        className="ai-message-content"
                                      />
                                    ) : (
                                      <div className="scene-speaking-bubble">
                                        <span>{getRandomSpeakingText()}</span>
                                        <div className="scene-loading-dots">
                                          <span></span>
                                          <span></span>
                                          <span></span>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                          
                          <div ref={messagesEndRef} />
                        </div>
                      </AutoHideScrollbar>
                      <ScrollButtons 
                        scrollContainerRef={scrollContainerRef} 
                        containerRef={chatContentRef}
                      />
                    </>
                  )}
                  
                  {/* 等待参会者发言状态 */}
                  {waitingForParticipants && (
                    <div className="scene-waiting-participants">
                      <span>等待参会者发言</span>
                      <div className="scene-loading-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                    </div>
                  )}


                </Content>

                {/* 底部输入区域 - 悬浮设计 */}
                {sceneData.status !== 2 && (
                  <div className={`scene-input-floating-container ${(showCommentsPanel || isCommenting || hasAIComment) ? 'scene-input-container-disabled' : ''}`}>
                    <div
                      ref={containerRef}
                      className="scene-input-area"
                      style={{
                        height: inputContainerHeight,
                        transition: isDragging ? 'none' : 'height 0.2s ease-out',
                      }}
                    >
                      {/* 拖拽指示器 */}
                      <div
                        className="scene-drag-indicator"
                        onMouseDown={handleDragStart}
                        onMouseEnter={() => setIsHovering(true)}
                        onMouseLeave={() => setIsHovering(false)}
                      >
                        {/* 三个点的指示器 */}
                        <div className="scene-drag-dots" style={{
                          opacity: isDragging || isHovering ? 1 : 0,
                        }}>
                          {[0, 1, 2].map((i) => (
                            <div key={i} className="scene-drag-dot" />
                          ))}
                        </div>
                      </div>

                      {/* 指定发言角色标签 - 放在顶部 */}
                      {selectedMentionCharacters.length > 0 && (
                        <div style={{
                          padding: '8px 12px',
                          borderBottom: '1px solid #f0f0f0',
                          display: 'flex',
                          flexWrap: 'wrap',
                          gap: '6px',
                          alignItems: 'center',
                          backgroundColor: '#fafafa',
                          justifyContent: 'space-between',
                        }}>
                          <div style={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: '6px',
                            alignItems: 'center',
                            flex: 1,
                          }}>
                            {selectedMentionCharacters.map(character => (
                              <Tag
                                key={character.id}
                                closable
                                onClose={() => handleRemoveMentionCharacter(character.id)}
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '4px',
                                  margin: 0,
                                  padding: '2px 8px',
                                  borderRadius: '12px',
                                  backgroundColor: '#f6f6f6',
                                  border: '1px solid #d9d9d9',
                                }}
                              >
                                {character.avatar ? (
                                  <Avatar src={character.avatar} size={16} />
                                ) : (
                                  <Avatar size={16} icon={<UserOutlined />} />
                                )}
                                <span style={{ fontSize: '12px' }}>@{character.name}</span>
                              </Tag>
                            ))}
                          </div>
                          {/* 清除所有角色按钮 */}
                          <Tooltip title="清除所有">
                            <Button
                              type="text"
                              size="small"
                              icon={<CloseOutlined />}
                              onClick={handleClearAllMentionCharacters}
                              style={{
                                padding: '4px',
                                minWidth: 'auto',
                                height: 'auto',
                                color: '#999',
                                fontSize: '12px',
                              }}
                            />
                          </Tooltip>
                        </div>
                      )}

                      {/* Textarea区域 */}
                      <div 
                        className="scene-textarea-container"
                        style={{
                          height: selectedMentionCharacters.length > 0 
                            ? `calc(100% - 88px)` // 减去拖拽指示器(4px) + 标签区域(44px) + function bar(40px) = 88px
                            : `calc(100% - 44px)`, // 减去拖拽指示器(4px) + function bar(40px) = 44px
                        }}
                      >
                        <Tooltip
                          title={getTooltipText()}
                          placement="top"
                          open={hasAIComment && messageText === ''}
                        >
                          <TextArea
                            ref={textareaRef}
                            id="scene-message-input"
                            name="messageText"
                            value={messageText}
                            onChange={(e) => setMessageText(e.target.value)}
                            placeholder={getPlaceholderText()}
                            disabled={!selectedCharacter}
                            onPressEnter={(e) => {
                              if (e.shiftKey) return;
                              e.preventDefault();
                              handleSendMessage();
                            }}
                            className="scene-input-textarea"
                            style={{
                               transition: isDragging ? 'none' : 'height 0.2s ease-out'
                             }}
                          />
                        </Tooltip>
                      </div>

                      {/* Function Bar - 固定在底部 */}
                      <div 
                        className="scene-function-bar"
                        style={{
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          height: '40px', // 固定高度
                        }}
                      >
                        <div className="scene-function-bar-left">
                           <Dropdown
                             menu={{ items: getMentionMenuItems() }}
                             placement="topLeft"
                             trigger={['click']}
                             disabled={!selectedCharacter}
                             onOpenChange={(open) => {
                               if (open) {
                                 setMentionTooltipOpen(false); // 打开Dropdown时关闭Tooltip
                               }
                             }}
                           >
                             <Tooltip
                               title="指定发言"
                               open={mentionTooltipOpen}
                               onOpenChange={(open) => setMentionTooltipOpen(open)}
                             >
                               <Button
                                 type="text"
                                 icon={<MdAlternateEmail />}
                                 className="scene-function-button"
                                 disabled={!selectedCharacter}
                                 onClick={() => setMentionTooltipOpen(false)} // 点击按钮时关闭Tooltip
                               />
                             </Tooltip>
                           </Dropdown>
                           <SpeechRecognitionButton
                             onTranscript={(text) => {
                               setMessageText(prev => prev + text);
                             }}
                             className="scene-function-button"
                           />
                         </div>
                        <div className="scene-function-bar-right">
                          <Button
                            type="primary"
                            icon={<SendOutlined />}
                            loading={sending}
                            onClick={handleSendMessage}
                            disabled={!selectedCharacter || !messageText.trim()}
                            className="scene-send-button"
                          >
                            发言
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </Layout>
            </Layout>

            {/* 练习指南抽屉 */}
            <GuideDrawer
              visible={guideDrawerVisible}
              onClose={() => setGuideDrawerVisible(false)}
              guides={sceneData.guides || []}
              title="练习指南"
              width="65%"
            />

            {/* 角色简介Modal */}
            <Modal
              title={
                <div className="scene-profile-modal-title">
                  <span>个人资料</span>
                </div>
              }
              open={profileModalVisible}
              onCancel={() => setProfileModalVisible(false)}
              footer={null}
              width={600}
            >
              {selectedCharacterForProfile && (
                <div className="scene-profile-content">
                  <div className="scene-profile-header">
                    {getCharacterAvatar(selectedCharacterForProfile)}
                    <div className="scene-profile-info">
                      <Title level={4}>
                        {selectedCharacterForProfile.name}
                      </Title>
                    </div>
                  </div>
                  <div className="scene-profile-description">
                    {selectedCharacterForProfile.profile || "暂无简介"}
                  </div>
                </div>
              )}
            </Modal>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default Scene;
